import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import { getWorkExperience } from '../../../utils/resumeData';
import { format } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';

const TimelineSection = () => {
  const [expandedEntry, setExpandedEntry] = useState(null);
  const timelineData = getWorkExperience();

  const toggleEntry = (entryId) => {
    setExpandedEntry(prev => prev === entryId ? null : entryId);
  };

  const renderHighlight = (highlight, index) => {
    const isResponsibility = highlight.startsWith('• ');
    const content = isResponsibility ? highlight.substring(2) : highlight;
    
    return (
      <motion.li 
        key={index}
        className="flex items-start space-x-3 mb-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
      >
        <span className={`mt-1 flex-shrink-0 ${isResponsibility ? 'text-accent' : 'text-primary'}`}>
          <Icon name={isResponsibility ? 'CheckCircle' : 'Award'} size={16} />
        </span>
        <span className="text-text-secondary">{content}</span>
      </motion.li>
    );
  };

  const formatDateRange = (startDate, endDate) => {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    const now = new Date();
    
    // Format individual date
    const formatDate = (date) => {
      if (!date) return 'Present';
      return format(date, 'MMM yyyy');
    };
    
    // Calculate duration in years and months
    const calculateDuration = (start, end) => {
      if (!start) return '';
      
      const endDate = end || now;
      let months = (endDate.getFullYear() - start.getFullYear()) * 12;
      months -= start.getMonth() + 1;
      months += endDate.getMonth() + 1;
      
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      
      const parts = [];
      if (years > 0) parts.push(`${years} ${years === 1 ? 'yr' : 'yrs'}`);
      if (remainingMonths > 0) parts.push(`${remainingMonths} ${remainingMonths === 1 ? 'mo' : 'mos'}`);
      
      return parts.length > 0 ? `• ${parts.join(' ')}` : '';
    };
    
    const startFormatted = formatDate(start);
    const endFormatted = formatDate(end);
    const duration = calculateDuration(start, end);
    
    return (
      <div className="flex flex-col sm:flex-row sm:items-center gap-1.5 whitespace-nowrap">
        <span className="flex-shrink-0">{startFormatted} — {endFormatted}</span>
        {duration && <span className="text-xs opacity-70 flex-shrink-0">{duration}</span>}
      </div>
    );
  };

  const getCompanyLogo = (companyName) => {
    const logoMap = {
      'AHEAD': 'https://media.licdn.com/dms/image/C4E0BAQHQr8ZhqvRKdA/company-logo_200_200/0/1630639068639/ahead_2_logo?e=2147483647&v=beta&t=XYvVJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQ',
      'Amazon Web Services (AWS)': 'https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg',
      'Red Hat': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Red_Hat_Logo_Lockup_RGB_Red.svg/1200px-Red_Hat_Logo_Lockup_RGB_Red.svg.png',
      'FICO': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ab/FICO_Logo.svg/1200px-FICO_Logo.svg.png',
      'American Express': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/American_Express_logo_%282018%29.svg/1200px-American_Express_logo_%282018%29.svg.png',
      'VCE': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/VCE_company_logo.svg/1200px-VCE_company_logo.svg.png',
      'Microsoft': 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/96/Microsoft_logo_%282012%29.svg/1200px-Microsoft_logo_%282012%29.svg.png'
    };
    return logoMap[companyName] || '/images/company-placeholder.png';
  };

  const getCompanyUrl = (companyName) => {
    const urlMap = {
      'AHEAD': 'https://www.thinkahead.com',
      'Amazon Web Services (AWS)': 'https://aws.amazon.com',
      'Red Hat': 'https://www.redhat.com',
      'FICO': 'https://www.fico.com',
      'American Express': 'https://www.americanexpress.com',
      'VCE': 'https://www.delltechnologies.com',
      'Microsoft': 'https://www.microsoft.com'
    };
    return urlMap[companyName] || '#';
  };

  return (
    <section id="experience" className="py-16 lg:py-24 bg-gradient-to-b from-background via-background/95 to-background/90 overflow-hidden relative">
      <div className="absolute inset-0 opacity-5 pointer-events-none" style={{
        backgroundImage: 'radial-gradient(rgba(99, 102, 241, 0.4) 1px, transparent 1px)',
        backgroundSize: '20px 20px',
      }}></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <span className="inline-block text-accent text-sm font-medium mb-3 px-3 py-1 rounded-full bg-accent/10">
            Professional Journey
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
            Work Experience
          </h2>
          <p className="text-lg text-text-secondary/90 max-w-3xl mx-auto leading-relaxed">
            A journey through my career, highlighting key roles, achievements, and the impact I've made at leading technology companies.
          </p>
        </motion.div>

        <div className="relative max-w-5xl mx-auto">
          {/* Animated vertical line */}
          <motion.div 
            className="absolute left-0 md:left-1/2 w-0.5 h-full bg-gradient-to-b from-accent/20 via-accent/40 to-accent/20 hidden md:block"
            initial={{ height: 0 }}
            whileInView={{ height: '100%' }}
            viewport={{ once: true }}
            transition={{ duration: 1.2, ease: [0.16, 1, 0.3, 1] }}
          ></motion.div>
          
          <div className="space-y-16 md:space-y-20">
            {timelineData.map((entry, index) => {
              const isEven = index % 2 === 0;
              const companyUrl = getCompanyUrl(entry.company);
              const logoUrl = getCompanyLogo(entry.company);
              
              return (
                <motion.div 
                  key={entry.id}
                  className={`relative group ${isEven ? 'md:pr-8 md:text-right' : 'md:pl-8'}`}
                  style={{ 
                    marginLeft: isEven ? '0' : 'auto',
                    width: '100%',
                    maxWidth: 'calc(50% - 40px)'
                  }}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: '-100px' }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  {/* Animated timeline dot */}
                  <motion.div 
                    className={`absolute top-6 w-4 h-4 bg-accent rounded-full border-4 border-background z-10 hidden md:flex items-center justify-center ${
                      isEven ? 'right-[-8px]' : 'left-[-8px]'
                    }`}
                    whileHover={{ scale: 1.3, boxShadow: '0 0 0 6px rgba(99, 102, 241, 0.2)' }}
                    transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                  >
                    <div className="absolute inset-0 rounded-full bg-accent/20 animate-ping"></div>
                  </motion.div>

                  <div className={`relative ${isEven ? 'md:mr-12' : 'md:ml-12'}`}>
                    <motion.div 
                      className="bg-surface/95 backdrop-blur-sm rounded-2xl border border-border/30 overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:border-accent/40 group-hover:shadow-accent/5 group-hover:-translate-y-1"
                      whileHover={{ 
                        boxShadow: '0 20px 40px -15px rgba(99, 102, 241, 0.2)',
                        borderColor: 'rgba(99, 102, 241, 0.4)'
                      }}
                    >
                      <div className="p-10 md:p-12 relative">
                        {/* Subtle gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-background/30 to-transparent opacity-30 pointer-events-none"></div>
                        
                        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6 mb-8 relative z-10">
                          <div className="flex items-start space-x-8">
                            <motion.div 
                              className="w-24 h-24 rounded-2xl overflow-hidden border border-border/30 bg-white p-2.5 flex-shrink-0 shadow-sm group-hover:shadow-md transition-all duration-300"
                              whileHover={{ rotate: 5, scale: 1.05 }}
                            >
                              <Image
                                src={logoUrl}
                                alt={`${entry.company} logo`}
                                className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                                width={80}
                                height={80}
                              />
                            </motion.div>
                            <div>
                              <h3 className="text-xl font-bold text-primary group-hover:text-accent transition-colors duration-300">
                                {entry.position}
                              </h3>
                              <div className="flex items-center space-x-2 mt-1">
                                <a 
                                  href={companyUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-accent/90 hover:text-accent font-medium flex items-center group/link transition-colors duration-200"
                                >
                                  {entry.company}
                                  <Icon 
                                    name="ExternalLink" 
                                    size={14} 
                                    className="ml-1.5 opacity-70 group-hover/link:opacity-100 group-hover/link:translate-x-0.5 transition-all duration-200" 
                                  />
                                </a>
                              </div>
                            </div>
                          </div>
                          
                          <motion.div 
                            className="relative min-w-0"
                            whileHover={{ scale: 1.02 }}
                          >
                            <div className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-accent/5 text-accent/90 border border-accent/10 group-hover:bg-accent/10 group-hover:border-accent/20 transition-all duration-300">
                              <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                                {formatDateRange(entry.startDate, entry.endDate)}
                                {!entry.endDate && (
                                  <span className="flex-shrink-0 flex h-2 w-2 relative">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-accent/70 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-2 w-2 bg-accent"></span>
                                  </span>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        </div>

                        <motion.div 
                          className="mt-8"
                          initial={{ opacity: 0, y: 10 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          viewport={{ once: true }}
                          transition={{ delay: 0.1 }}
                        >
                          <p className="text-text-secondary/90 leading-relaxed text-[16px] md:text-[16.5px] tracking-wide">
                            {entry.summary}
                          </p>
                        </motion.div>

                        {entry.highlights && entry.highlights.length > 0 && (
                          <motion.div 
                            className="mt-8"
                            initial={{ opacity: 0, y: 10 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ delay: 0.15 }}
                          >
                            <h4 className="text-xs font-semibold text-primary/80 mb-4 uppercase tracking-wider flex items-center">
                              <span className="w-8 h-px bg-gradient-to-r from-accent/40 to-accent/0 mr-3"></span>
                              Key Achievements
                              <span className="w-8 h-px bg-gradient-to-l from-accent/40 to-accent/0 ml-3"></span>
                            </h4>
                            <ul className="space-y-5">
                              {entry.highlights.slice(0, 3).map((highlight, i) => (
                                <motion.li 
                                  key={i} 
                                  className="flex items-start space-x-3 group/achievement"
                                  initial={{ opacity: 0, x: -5 }}
                                  whileInView={{ opacity: 1, x: 0 }}
                                  viewport={{ once: true }}
                                  transition={{ delay: 0.1 + (i * 0.05) }}
                                >
                                  <span className="text-accent mt-1 flex-shrink-0 group-hover/achievement:scale-110 transition-transform duration-200">
                                    <Icon name="CheckCircle" size={16} />
                                  </span>
                                  <span className="text-text-secondary/90 group-hover/achievement:text-text-primary transition-colors duration-200">
                                    {highlight}
                                  </span>
                                </motion.li>
                              ))}
                            </ul>
                          </motion.div>
                        )}

                        {entry.technologies && entry.technologies.length > 0 && (
                          <motion.div 
                            className="mt-8"
                            initial={{ opacity: 0, y: 10 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ delay: 0.2 }}
                          >
                            <h4 className="text-xs font-semibold text-primary/80 mb-3 uppercase tracking-wider flex items-center">
                              <span className="w-8 h-px bg-gradient-to-r from-accent/40 to-accent/0 mr-3"></span>
                              Technologies & Skills
                              <span className="w-8 h-px bg-gradient-to-l from-accent/40 to-accent/0 ml-3"></span>
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {entry.technologies.slice(0, 8).map((tech, i) => (
                                <motion.span
                                  key={i}
                                  className="px-3 py-1.5 bg-background/80 text-text-secondary/90 text-xs font-medium rounded-full border border-border/30 hover:bg-accent/5 hover:text-accent hover:border-accent/30 transition-all duration-200 cursor-default"
                                  initial={{ scale: 0.9, opacity: 0 }}
                                  whileInView={{ scale: 1, opacity: 1 }}
                                  viewport={{ once: true }}
                                  transition={{ delay: 0.1 + (i * 0.02) }}
                                  whileHover={{ 
                                    y: -2,
                                    boxShadow: '0 4px 12px -2px rgba(99, 102, 241, 0.15)'
                                  }}
                                >
                                  {tech}
                                </motion.span>
                              ))}
                              {entry.technologies.length > 8 && (
                                <motion.span 
                                  className="px-3 py-1.5 bg-background/50 text-text-tertiary/80 text-xs font-medium rounded-full border border-dashed border-border/40 hover:border-accent/40 hover:text-accent/90 transition-colors duration-200"
                                  initial={{ scale: 0.9, opacity: 0 }}
                                  whileInView={{ scale: 1, opacity: 1 }}
                                  viewport={{ once: true }}
                                  transition={{ delay: 0.1 + (8 * 0.02) }}
                                >
                                  +{entry.technologies.length - 8} more
                                </motion.span>
                              )}
                            </div>
                          </motion.div>
                        )}

                        <AnimatePresence>
                          {String(expandedEntry) === String(entry.id) && (
                            <motion.div
                              className="mt-6 pt-6 border-t border-border/20"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{
                                opacity: 1,
                                height: 'auto',
                                transition: { duration: 0.3 }
                              }}
                              exit={{
                                opacity: 0,
                                height: 0,
                                transition: { duration: 0.2 }
                              }}
                              style={{ overflow: 'hidden' }}
                            >
                              <div className="space-y-6">
                                {entry.highlights && entry.highlights.length > 0 && (
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: 0.1 }}
                                  >
                                    <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider flex items-center">
                                      <Icon name="Award" size={16} className="mr-2" />
                                      Key Responsibilities & Achievements
                                    </h4>
                                    <ul className="space-y-3">
                                      {entry.highlights.map((highlight, i) => renderHighlight(highlight, i))}
                                    </ul>
                                  </motion.div>
                                )}

                                {entry.technologies && entry.technologies.length > 0 && (
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: 0.2 }}
                                  >
                                    <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider flex items-center">
                                      <Icon name="Code" size={16} className="mr-2" />
                                      Technologies & Tools
                                    </h4>
                                    <div className="flex flex-wrap gap-2">
                                      {entry.technologies.map((tech, i) => (
                                        <motion.span
                                          key={i}
                                          className="px-3 py-1.5 bg-background text-text-secondary text-xs font-medium rounded-full border border-border/30 hover:bg-accent/5 hover:border-accent/30 transition-colors"
                                          initial={{ opacity: 0, scale: 0.9 }}
                                          animate={{ opacity: 1, scale: 1 }}
                                          transition={{ 
                                            duration: 0.2, 
                                            delay: 0.1 + (i * 0.02)
                                          }}
                                        >
                                          {tech}
                                        </motion.span>
                                      ))}
                                    </div>
                                  </motion.div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <AnimatePresence>
                          {String(expandedEntry) === String(entry.id) && (
                            <motion.div
                              className="mt-6 pt-6 border-t border-border/20"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{
                                opacity: 1,
                                height: 'auto',
                                transition: { duration: 0.3 }
                              }}
                              exit={{
                                opacity: 0,
                                height: 0,
                                transition: { duration: 0.2 }
                              }}
                              style={{ overflow: 'hidden' }}
                            >
                              <div className="space-y-4">
                                <h4 className="text-sm font-semibold text-primary/80 mb-3 flex items-center">
                                  <span className="w-4 h-px bg-gradient-to-r from-accent/40 to-accent/0 mr-2"></span>
                                  Detailed Achievements
                                  <span className="w-4 h-px bg-gradient-to-l from-accent/40 to-accent/0 ml-2"></span>
                                </h4>
                                <ul className="space-y-3">
                                  {entry.highlights.slice(3).map((highlight, i) => (
                                    <motion.li 
                                      key={i + 3}
                                      className="flex items-start space-x-3 group/achievement"
                                      initial={{ opacity: 0, x: -5 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: 0.1 + (i * 0.05) }}
                                    >
                                      <span className="text-accent mt-1 flex-shrink-0 group-hover/achievement:scale-110 transition-transform duration-200">
                                        <Icon name="CheckCircle" size={16} />
                                      </span>
                                      <span className="text-text-secondary/90 group-hover/achievement:text-text-primary transition-colors duration-200">
                                        {highlight}
                                      </span>
                                    </motion.li>
                                  ))}
                                </ul>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        {entry.highlights && entry.highlights.length > 3 && (
                          <motion.button
                            onClick={() => toggleEntry(entry.id)}
                            className="mt-6 inline-flex items-center text-sm font-medium text-accent/90 hover:text-accent transition-colors duration-200 group"
                            whileHover={{ x: 2 }}
                          >
                            {String(expandedEntry) === String(entry.id) ? (
                              <>
                                <span>Show Less</span>
                                <Icon 
                                  name="ChevronUp" 
                                  size={16} 
                                  className="ml-1.5 group-hover:-translate-y-0.5 transition-all duration-200" 
                                />
                              </>
                            ) : (
                              <>
                                <span>View All Achievements</span>
                                <Icon 
                                  name="ChevronDown" 
                                  size={16} 
                                  className="ml-1.5 group-hover:translate-y-0.5 transition-all duration-200" 
                                />
                              </>
                            )}
                          </motion.button>
                        )}
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TimelineSection;
