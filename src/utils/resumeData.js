import resumeJson from '../../resume.json';

/**
 * Utility functions to access and format resume data
 */
export const getResumeData = () => {
  return resumeJson;
};

export const getBasicInfo = () => {
  return resumeJson.basics;
};

export const getWorkExperience = () => {
  return resumeJson.work.map((job, index) => ({
    id: index + 1,
    position: job.position,  // Changed from 'role' to 'position'
    company: job.name,
    startDate: job.startDate,  // Added startDate
    endDate: job.endDate,      // Added endDate
    period: formatDateRange(job.startDate, job.endDate),
    location: job.location || 'Remote',
    type: job.endDate ? 'Full-time' : 'Current',
    summary: job.summary,      // Changed from 'description' to 'summary'
    highlights: job.highlights || extractAchievements(job.summary),  // Changed from 'achievements' to 'highlights'
    technologies: extractTechnologies(job.summary),
    companyLogo: getCompanyLogo(job.name),
    url: job.url
  }));
};

export const getSkills = () => {
  const skills = resumeJson.skills;
  
  // Group skills by category
  const technicalSkills = [
    'OpenStack', 'Amazon Web Services (AWS)', 'Kubernetes', 'Docker', 'OpenShift',
    'Amazon EKS', 'Containers', 'GitOps', 'CI/CD', 'DevOps', 'Linux', 'Red Hat Linux',
    'Red Hat Enterprise Linux (RHEL)', 'Windows Server', 'VMware', 'Virtualization'
  ];
  
  const cloudSkills = [
    'Cloud Computing', 'Cloud Computing IaaS', 'Hybrid Cloud', 'Private Cloud',
    'Cloud Development', 'Cloud Applications', 'Cloud Security', 'IaaS', 'PaaS',
    'Amazon Bedrock'
  ];
  
  const aiMlSkills = [
    'Artificial Intelligence (AI)', 'Machine Learning', 'Large Language Models (LLM)'
  ];
  
  const architectureSkills = [
    'Distributed Systems', 'Systems Engineering', 'Open Systems Architecture',
    'Converged Infrastructure', 'Software Defined Storage', 'Software Defined Networking',
    'Load Balancing'
  ];
  
  const managementSkills = [
    'Management', 'Business Ownership', 'Professional Services', 'Requirements Gathering',
    'Defining Requirements', 'Research', 'Engineering', 'Capacity Planning',
    'Software Quality Assurance', 'Release Engineering'
  ];
  
  return {
    technical: {
      title: 'Cloud & Infrastructure',
      icon: 'Cloud',
      skills: technicalSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Technical',
        years: getSkillYears(skill)
      }))
    },
    cloud: {
      title: 'Platform Engineering',
      icon: 'Server',
      skills: cloudSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Cloud',
        years: getSkillYears(skill)
      }))
    },
    ai: {
      title: 'AI & Machine Learning',
      icon: 'Brain',
      skills: aiMlSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'AI/ML',
        years: getSkillYears(skill)
      }))
    },
    architecture: {
      title: 'Architecture & Design',
      icon: 'Layout',
      skills: architectureSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Architecture',
        years: getSkillYears(skill)
      }))
    },
    management: {
      title: 'Leadership & Management',
      icon: 'Users',
      skills: managementSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Management',
        years: getSkillYears(skill)
      }))
    }
  };
};

export const getCertifications = () => {
  return resumeJson.certificates.map((cert, index) => ({
    id: index + 1,
    name: cert.name,
    issuer: cert.issuer,
    date: cert.startDate ? new Date(cert.startDate).getFullYear().toString() : 'N/A',
    icon: 'Award',
    verified: true,
    url: cert.url
  }));
};

export const getAwards = () => {
  return resumeJson.awards || [];
};

export const getPublications = () => {
  return resumeJson.publications || [];
};

export const getVolunteerWork = () => {
  return resumeJson.volunteer || [];
};

export const getReferences = () => {
  return resumeJson.references || [];
};

// Helper functions
function formatDateRange(startDate, endDate) {
  const start = new Date(startDate);
  const end = endDate ? new Date(endDate) : null;
  
  const startYear = start.getFullYear();
  const endYear = end ? end.getFullYear() : 'Present';
  
  if (startYear === endYear || endYear === 'Present') {
    return `${startYear} - ${endYear}`;
  }
  
  return `${startYear} - ${endYear}`;
}

function extractAchievements(summary) {
  if (!summary) return [];
  
  // Extract bullet points or key achievements from summary
  const achievements = [];
  const lines = summary.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    if (line.includes('•') || line.includes('-') || line.includes('*')) {
      achievements.push(line.replace(/[•\-*]/, '').trim());
    } else if (line.length > 50 && line.length < 200) {
      achievements.push(line.trim());
    }
  });
  
  return achievements.slice(0, 5); // Limit to 5 achievements
}

function extractTechnologies(summary) {
  if (!summary) return [];
  
  const techKeywords = [
    'AWS', 'OpenStack', 'Kubernetes', 'Docker', 'OpenShift', 'Azure', 'GCP',
    'Python', 'JavaScript', 'React', 'Node.js', 'Linux', 'VMware', 'Ansible',
    'Terraform', 'Jenkins', 'Git', 'CI/CD', 'DevOps', 'Microservices',
    'Containers', 'API', 'REST', 'GraphQL', 'MongoDB', 'PostgreSQL', 'Redis'
  ];
  
  const foundTech = [];
  techKeywords.forEach(tech => {
    if (summary.toLowerCase().includes(tech.toLowerCase())) {
      foundTech.push(tech);
    }
  });
  
  return foundTech.slice(0, 8); // Limit to 8 technologies
}

function getCompanyLogo(companyName) {
  const logoMap = {
    'AHEAD': 'https://media.licdn.com/dms/image/v2/C4E0BAQHQr8ZhqvRKdA/company-logo_200_200/company-logo_200_200/0/1630639068639/ahead_2_logo?e=1743638400&v=beta&t=XYvVJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQ',
    'Amazon Web Services (AWS)': 'https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg',
    'Red Hat': 'https://upload.wikimedia.org/wikipedia/commons/d/d8/Red_Hat_logo.svg',
    'FICO': 'https://upload.wikimedia.org/wikipedia/commons/f/f4/FICO_logo.svg',
    'American Express': 'https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg',
    'VCE': 'https://via.placeholder.com/48x48/0066cc/ffffff?text=VCE',
    'Microsoft': 'https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg'
  };
  
  return logoMap[companyName] || 'https://via.placeholder.com/48x48/666666/ffffff?text=' + companyName.charAt(0);
}

function getSkillLevel(skillName) {
  // Assign skill levels based on experience and importance
  const highLevel = ['Amazon Web Services (AWS)', 'OpenStack', 'Kubernetes', 'Cloud Computing', 'Linux'];
  const mediumLevel = ['Docker', 'OpenShift', 'DevOps', 'Virtualization', 'Management'];
  
  if (highLevel.includes(skillName)) return 90 + Math.floor(Math.random() * 10);
  if (mediumLevel.includes(skillName)) return 80 + Math.floor(Math.random() * 10);
  return 70 + Math.floor(Math.random() * 15);
}

function getSkillYears(skillName) {
  // Estimate years based on career progression
  const seniorSkills = ['Management', 'Cloud Computing', 'Linux', 'Virtualization'];
  const midSkills = ['Kubernetes', 'Docker', 'OpenShift', 'DevOps'];
  
  if (seniorSkills.includes(skillName)) return '10+';
  if (midSkills.includes(skillName)) return '5+';
  return '3+';
}

export const getCareerStats = () => {
  const workExperience = resumeJson.work;
  const startYear = Math.min(...workExperience.map(job => new Date(job.startDate).getFullYear()));
  const currentYear = new Date().getFullYear();
  const yearsExperience = currentYear - startYear;
  
  return {
    yearsExperience,
    companiesWorked: workExperience.length,
    certifications: resumeJson.certificates.length,
    publications: resumeJson.publications?.length || 0
  };
};